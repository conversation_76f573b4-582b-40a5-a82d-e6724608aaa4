{"name": "formula1-app", "version": "1.0.0", "description": "An app for Formula 1 world Championship related statistics", "type": "module", "private": true, "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "scripts": {"lint": "eslint '{src,tools}/**/*.{ts,tsx,astro}'", "check": "astro check && tsc --noEmit", "dev": "astro dev", "start": "astro dev", "data:gen": "node --experimental-strip-types ./tools/generate-data.ts --allQueries", "data:up": "node --experimental-strip-types ./tools/update-data.ts", "build": "NODE_ENV=production astro build", "fmt": "prettier --plugin-search-dir=. --write 'src/**/*.{ts,tsx,astro,json}'", "fmtc": "prettier --plugin-search-dir=. --check 'src/**/*.{ts,tsx,astro,json}'"}, "dependencies": {"@astrojs/preact": "^4.1.3", "@astrojs/vercel": "^9.0.1", "astro": "^5.15.9", "change-case": "^5.4.4", "dayjs": "^1.11.19", "del": "^8.0.1", "globby": "^16.0.0", "just-pick": "^4.2.0", "normalize.css": "^8.0.1", "preact": "^10.27.2", "sass": "^1.94.2", "typescript": "^5.9.3"}, "devDependencies": {"@astrojs/check": "^0.9.5", "@cspell/eslint-plugin": "^9.3.2", "@eslint/js": "^9.39.1", "@types/cli-progress": "^3.11.6", "@types/node": "^24.10.1", "@types/papaparse": "^5.5.0", "@types/prompts": "^2.4.9", "@typescript-eslint/eslint-plugin": "^8.47.0", "@typescript-eslint/parser": "^8.47.0", "chalk": "^5.6.2", "cheerio": "1.1.2", "cli-progress": "^3.12.0", "eslint": "^9.39.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-astro": "^1.5.0", "eslint-plugin-simple-import-sort": "^12.1.1", "fast-json-stable-stringify": "^2.1.0", "globals": "^16.5.0", "just-omit": "^2.2.0", "just-safe-get": "^4.2.0", "ky": "^1.14.0", "meow": "^14.0.0", "papaparse": "^5.5.3", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.14.1", "prompts": "^2.4.2", "typescript": "^5.1.3", "typescript-eslint": "^8.47.0", "zx": "^8.8.5"}, "pnpm": {"overrides": {"path-to-regexp": "6.3.0"}, "onlyBuiltDependencies": ["@parcel/watcher", "esbuild", "sharp"]}}