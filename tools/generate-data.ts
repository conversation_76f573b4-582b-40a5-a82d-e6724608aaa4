import fs from 'node:fs/promises';
import path from 'node:path';
import { DatabaseSync } from 'node:sqlite';

import { kebabCase, snakeCase } from 'change-case';
import { Presets, SingleBar } from 'cli-progress';
import { deleteAsync } from 'del';
// import dayjs from 'dayjs';
// import advancedFormat from 'dayjs/plugin/advancedFormat';
import stringify from 'fast-json-stable-stringify';
import pick from 'just-pick';
import get from 'just-safe-get';
import meow from 'meow';
import prettier from 'prettier';
import prompts from 'prompts';

import driverImages from './driver-images.json' with { type: 'json' };
import { __DIRNAME, DB_FILE } from './shared-utils.ts';
/** CLI INIT ******************************************************************/
// dayjs.extend(advancedFormat);
const cli = meow('', {
	importMeta: import.meta,
	flags: {
		query: { type: 'string', isMultiple: true },
	},
});

const { query: queries = [], allQueries } = cli.flags;

/** Common Functions **********************************************************/
async function readQueryFile(file: string): Promise<string> {
	return fs.readFile(path.resolve(__DIRNAME, `queries/${file}`), 'utf8');
}

async function writeDataFile(filepath: string, data: unknown): Promise<void> {
	await fs.mkdir(path.dirname(filepath), { recursive: true });
	const formattedCode = await prettier.format(stringify(data), {
		printWidth: 80,
		useTabs: true,
		parser: 'json',
	});

	return fs.writeFile(filepath, formattedCode, 'utf8');
}

export function slugify(str: string): string {
	return kebabCase(str)
		.normalize('NFD')
		.replace(/[\u0300-\u036f]/g, '');
}

/** Query Functions ***********************************************************/
const bar = new SingleBar({}, Presets.shades_classic);

const queryFns: Record<string, (db: DatabaseSync) => Promise<void>> = {
	async homepage(db) {
		const homepageConstructorChampionsQuery = await readQueryFile(
			'homepage_constructor_champions.sql',
		);
		const homepageConstructorsQuery = await readQueryFile(
			'homepage_constructors.sql',
		);
		const homepageDriverChampionsQuery = await readQueryFile(
			'homepage_driver_champions.sql',
		);
		const homepageDriversQuery = await readQueryFile('homepage_drivers.sql');
		const homepageRacesQuery = await readQueryFile('homepage_races.sql');
		const homepageSeasonsQuery = await readQueryFile('homepage_seasons.sql');

		const data = {
			constructorChampions: db.prepare(homepageConstructorChampionsQuery).all()
				.length,
			constructors: db.prepare(homepageConstructorsQuery).all().length,
			driverChampions: db.prepare(homepageDriverChampionsQuery).all().length,
			drivers: db.prepare(homepageDriversQuery).all().length,
			races: db.prepare(homepageRacesQuery).all().length,
			seasons: db.prepare(homepageSeasonsQuery).all().length,
		};

		return writeDataFile(
			path.resolve(__DIRNAME, '../src/data/homepage.json'),
			data,
		);
	},

	async seasons_list(db) {
		const query = await readQueryFile('seasons_list.sql');

		return writeDataFile(
			path.resolve(__DIRNAME, '../src/data/seasons-list.json'),
			db.prepare(query).all(),
		);
	},

	async seasons(db) {
		const seasonsQuery = await readQueryFile('seasons_list.sql');
		const seasons = db.prepare(seasonsQuery).all();
		const dataDir = path.resolve(__DIRNAME, `../src/content/seasons`);

		bar.start(seasons.length, 0);

		let i = 0;

		await deleteAsync(path.join(dataDir, '*', 'season.json'));

		const seasonRoundsQuery = await readQueryFile('season_rounds.sql');
		const seasonTeamsQuery = await readQueryFile('season_teams.sql');

		for (const row of seasons) {
			bar.update(++i);

			const year = Number(row.year ?? 0);

			const seasonData = {
				rounds: db
					.prepare(seasonRoundsQuery)
					.all({ year })
					.map((d) => ({ ...d, id: d.id ? slugify(String(d.id)) : 'unknown' })),
				teams: db
					.prepare(seasonTeamsQuery)
					.all({ year })
					.map((d) => ({
						...d,
						constructorRef: snakeCase(String(d.constructorRef)),
						drivers: JSON.parse(d.drivers ? String(d.drivers) : '[]').map(
							// eslint-disable-next-line @typescript-eslint/no-explicit-any
							(d: any) => ({
								...d,
								driverRef: snakeCase(String(d.driverRef)),
							}),
						),
					})),
				year: row.year,
			};

			await writeDataFile(
				path.resolve(dataDir, `${row.year}/season.json`),
				seasonData,
			);
		}

		bar.stop();
	},
	async drivers(): Promise<void> {
		const driversQuery = await readQueryFile('drivers_list.sql');
		const drivers = db.prepare(driversQuery).all();

		// const driversQuery = await readQueryFile('drivers.sql');
		const dataDir = path.resolve(__DIRNAME, `../src/content/drivers`);

		bar.start(drivers.length, 0);

		let i = 0;

		// await deleteAsync(path.join(dataDir));

		const driverQuery = await readQueryFile('drivers_driver.sql');

		for (const driver of drivers) {
			const driverRef = String(driver.driverRef);
			bar.update(++i);
			const driverData = db
				.prepare(driverQuery)
				.all(pick(driver, ['driverId']));

			await writeDataFile(path.resolve(dataDir, `${driverRef}/driver.json`), {
				...driverData[0],
				image: get(driverImages, driverRef),
			});
			// const year = Number(season.year ?? 0);

			// for (const driver of data) {
			// 	bar.update(++i);

			// 	await writeDataFile(
			// 		path.resolve(dataDir, `${driver.driverRef}.json`),
			// 		driver,
			// 	);
			// }
		}

		bar.stop();
	},
	async constructors() {
		const data = [];
		// const data = dataGenerator.getConstructorsInstance().getData();
		const dataDir = path.resolve(__DIRNAME, `../src/content/constructors`);

		bar.start(data.length, 0);
		let i = 0;

		// await cleanDataDir(dataDir);

		for (const row of data) {
			bar.update(++i);

			// await writeDataFile(
			// 	path.resolve(dataDir, `${row.constructorRef}.json`),
			// 	dataGenerator.getConstructorData(row),
			// );
		}

		bar.stop();
	},
	async races() {
		const seasonsQuery = await readQueryFile('seasons_list.sql');
		const seasons = db.prepare(seasonsQuery).all();
		const dataDir = path.resolve(__DIRNAME, `../src/content/seasons`);

		bar.start(seasons.length, 0);
		let i = 0;

		await deleteAsync([
			path.join(dataDir, '*', '*.json'),
			`!${path.join(dataDir, '*', 'season.json')}`,
		]);

		for (const season of seasons) {
			bar.update(++i);

			// const slug = slugify(race.name);

			// await writeDataFile(
			// 	path.resolve(dataDir, `${race.year}/${slug}.json`),
			// 	dataGenerator.getRoundData(race, slug),
			// );
		}

		bar.stop();
	},
};

/** CLI Logic *****************************************************************/
const LIST_QUERIES = Object.keys(queryFns);

if (queries.length === 0 && !allQueries) {
	const responses = await prompts([
		{
			name: 'query',
			type: 'multiselect',
			message: 'Please select all the queries you want to generate',
			choices: LIST_QUERIES.map((value) => ({ value, title: value })),
		},
	]);

	queries.splice(0, queries.length, ...responses.query);
} else if (allQueries) {
	queries.splice(0, queries.length, ...LIST_QUERIES);
}

if (queries.length === 0) {
	// eslint-disable-next-line no-console
	console.log('No query selected. Exiting!');
	process.exit(0);
}

const db = new DatabaseSync(DB_FILE);
for (const query of queries) {
	try {
		// eslint-disable-next-line no-console
		console.log(`Executing ${query} query...`);
		await queryFns[query]?.(db);
	} catch (e) {
		// eslint-disable-next-line no-console
		console.log(e);
		process.exit(1);
	}
}

process.exit(0);
