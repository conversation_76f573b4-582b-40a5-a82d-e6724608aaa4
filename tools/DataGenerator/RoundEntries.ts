import { z } from 'zod';

import { BaseData } from './BaseData';

const RoundEntrySchema = z.object({
	id: z.number(),
	car_number: z.number(),
	round_id: z.number(),
	team_driver_id: z.number(),
});

export type RoundEntry = z.infer<typeof RoundEntrySchema>;

export class RoundEntries extends BaseData<RoundEntry> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_roundentry.csv';

	protected override schema = RoundEntrySchema;
}
