import { z } from 'zod';

import { BaseData } from './BaseData';

const RoundEntrySchema = z.object({
	id: z.number(),
	car_number: z.number(),
	round_id: z.number(),
	team_driver_id: z.number(),
});

export type RoundEntry = z.infer<typeof RoundEntrySchema>;

export class RoundEntries extends BaseData<RoundEntry> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_roundentry.csv';

	protected override schema = RoundEntrySchema;

	constructor() {
		super();
		this.loadData();
	}

	public getData(): ReadonlyArray<RoundEntry> {
		return this.data;
	}

	public getRoundEntryById(id: number): RoundEntry | undefined {
		return this.data.find((entry) => entry.id === id);
	}

	public getRoundEntriesByRoundId(roundId: number): ReadonlyArray<RoundEntry> {
		return this.data.filter((entry) => entry.round_id === roundId);
	}

	public getRoundEntriesByTeamDriverId(teamDriverId: number): ReadonlyArray<RoundEntry> {
		return this.data.filter((entry) => entry.team_driver_id === teamDriverId);
	}

	public getRoundEntryByCarNumber(roundId: number, carNumber: number): RoundEntry | undefined {
		return this.data.find((entry) => entry.round_id === roundId && entry.car_number === carNumber);
	}
}
