import { z } from 'zod';

import { BaseData } from './BaseData';

const ChampionshipAdjustmentSchema = z.object({
	id: z.number(),
	adjustment: z.number(),
	driver_id: z.number().nullable(),
	points: z.number().nullable(),
	season_id: z.number().nullable(),
	team_id: z.number().nullable(),
});

export type ChampionshipAdjustment = z.infer<
	typeof ChampionshipAdjustmentSchema
>;

export class ChampionshipAdjustments extends BaseData<ChampionshipAdjustment> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_championshipadjustment.csv';

	protected override schema = ChampionshipAdjustmentSchema;
}
