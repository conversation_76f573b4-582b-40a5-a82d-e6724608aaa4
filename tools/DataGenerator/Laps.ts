import { z } from 'zod';

import { BaseData } from './BaseData';

const LapSchema = z.object({
	id: z.number(),
	average_speed: z.number().nullable(),
	is_deleted: z.string(),
	is_entry_fastest_lap: z.string(),
	number: z.number().nullable(),
	position: z.number().nullable(),
	session_entry_id: z.number(),
	time: z.string().nullable(),
});

export type Lap = z.infer<typeof LapSchema>;

export class Laps extends BaseData<Lap> {
	protected override filename = 'formula_one_lap.csv';

	protected override schema = LapSchema;
}
