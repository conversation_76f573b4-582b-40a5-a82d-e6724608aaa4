import { z } from 'zod';

import { BaseData } from './BaseData';

const SessionEntrySchema = z.object({
	id: z.number(),
	detail: z.string().nullable(),
	fastest_lap_rank: z.number().nullable(),
	grid: z.number().nullable(),
	is_classified: z.string(),
	is_eligible_for_points: z.string(),
	laps_completed: z.number(),
	points: z.number(),
	position: z.number().nullable(),
	round_entry_id: z.number(),
	session_id: z.number(),
	status: z.number(),
	time: z.string().nullable(),
});

export type SessionEntry = z.infer<typeof SessionEntrySchema>;

export class SessionEntries extends BaseData<SessionEntry> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_sessionentry.csv';

	protected override schema = SessionEntrySchema;
}
