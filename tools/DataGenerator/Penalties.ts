import { z } from 'zod';

import { BaseData } from './BaseData';

const PenaltySchema = z.object({
	id: z.number(),
	earned_id: z.number().nullable(),
	is_time_served_in_pit: z.string().nullable(),
	license_points: z.number().nullable(),
	position: z.number().nullable(),
	served_id: z.number().nullable(),
	time: z.string().nullable(),
});

export type Penalty = z.infer<typeof PenaltySchema>;

export class Penalties extends BaseData<Penalty> {
	protected override filename = 'formula_one_penalty.csv';

	protected override schema = PenaltySchema;
}
