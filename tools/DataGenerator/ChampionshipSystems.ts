import { z } from 'zod';

import { BaseData } from './BaseData';

const ChampionshipSystemSchema = z.object({
	id: z.number(),
	driver_best_results: z.number(),
	driver_season_split: z.number(),
	eligibility: z.number(),
	name: z.string(),
	reference: z.string(),
	team_best_results: z.number(),
	team_points_per_session: z.number(),
	team_season_split: z.number(),
});

export type ChampionshipSystem = z.infer<typeof ChampionshipSystemSchema>;

export class ChampionshipSystems extends BaseData<ChampionshipSystem> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_championshipsystem.csv';

	protected override schema = ChampionshipSystemSchema;

	constructor() {
		super();
		this.loadData();
	}

	public getData(): ReadonlyArray<ChampionshipSystem> {
		return this.data;
	}

	public getChampionshipSystemById(id: number): ChampionshipSystem | undefined {
		return this.data.find((system) => system.id === id);
	}

	public getChampionshipSystemByReference(reference: string): ChampionshipSystem | undefined {
		return this.data.find((system) => system.reference === reference);
	}
}
