import { z } from 'zod';

import { BaseData } from './BaseData';

const PointSystemSchema = z.object({
	id: z.number(),
	driver_fastest_lap: z.number(),
	driver_position_points: z.number(),
	is_double_points: z.string(),
	name: z.string(),
	partial: z.number(),
	reference: z.string(),
	shared_drive: z.number(),
	team_fastest_lap: z.number(),
	team_position_points: z.number(),
});

export type PointSystem = z.infer<typeof PointSystemSchema>;

export class PointSystems extends BaseData<PointSystem> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_pointsystem.csv';

	protected override schema = PointSystemSchema;
}
