import { z } from 'zod';

import { BaseData } from './BaseData';

const TeamDriverSchema = z.object({
	id: z.number(),
	driver_id: z.number(),
	role: z.string().nullable(),
	season_id: z.number(),
	team_id: z.number(),
});

export type TeamDriver = z.infer<typeof TeamDriverSchema>;

export class TeamDrivers extends BaseData<TeamDriver> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_teamdriver.csv';

	protected override schema = TeamDriverSchema;

	constructor() {
		super();
		this.loadData();
	}

	public getData(): ReadonlyArray<TeamDriver> {
		return this.data;
	}

	public getTeamDriverById(id: number): TeamDriver | undefined {
		return this.data.find((teamDriver) => teamDriver.id === id);
	}

	public getTeamDriversByDriverId(driverId: number): ReadonlyArray<TeamDriver> {
		return this.data.filter((teamDriver) => teamDriver.driver_id === driverId);
	}

	public getTeamDriversByTeamId(teamId: number): ReadonlyArray<TeamDriver> {
		return this.data.filter((teamDriver) => teamDriver.team_id === teamId);
	}

	public getTeamDriversBySeasonId(seasonId: number): ReadonlyArray<TeamDriver> {
		return this.data.filter((teamDriver) => teamDriver.season_id === seasonId);
	}

	public getTeamDriverByDriverAndSeason(driverId: number, seasonId: number): ReadonlyArray<TeamDriver> {
		return this.data.filter(
			(teamDriver) => teamDriver.driver_id === driverId && teamDriver.season_id === seasonId
		);
	}

	public getTeamDriversByTeamAndSeason(teamId: number, seasonId: number): ReadonlyArray<TeamDriver> {
		return this.data.filter(
			(teamDriver) => teamDriver.team_id === teamId && teamDriver.season_id === seasonId
		);
	}
}
