import { z } from 'zod';

import { BaseData } from './BaseData';

const TeamDriverSchema = z.object({
	id: z.number(),
	driver_id: z.number(),
	role: z.string().nullable(),
	season_id: z.number(),
	team_id: z.number(),
});

export type TeamDriver = z.infer<typeof TeamDriverSchema>;

export class TeamDrivers extends BaseData<TeamDriver> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_teamdriver.csv';

	protected override schema = TeamDriverSchema;
}
