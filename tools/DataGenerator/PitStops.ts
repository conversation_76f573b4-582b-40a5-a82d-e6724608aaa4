import { z } from 'zod';

import { BaseData } from './BaseData';

const PitStopSchema = z.object({
	id: z.number(),
	duration: z.string(),
	lap_id: z.number(),
	local_timestamp: z.string(),
	number: z.number(),
	session_entry_id: z.number(),
});

export type PitStop = z.infer<typeof PitStopSchema>;

export class PitStops extends BaseData<PitStop> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_pitstop.csv';

	protected override schema = PitStopSchema;
}
