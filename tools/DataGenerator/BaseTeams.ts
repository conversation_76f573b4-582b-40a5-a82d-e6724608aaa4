import { z } from 'zod';

import { BaseData } from './BaseData';

const BaseTeamSchema = z.object({
	id: z.number(),
	name: z.string().nullable(),
});

export type BaseTeam = z.infer<typeof BaseTeamSchema>;

export class BaseTeams extends BaseData<BaseTeam> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_baseteam.csv';

	protected override schema = BaseTeamSchema;
}
