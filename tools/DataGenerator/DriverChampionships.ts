import { z } from 'zod';

import { BaseData } from './BaseData';

const DriverChampionshipSchema = z.object({
	id: z.number(),
	adjustment_type: z.number(),
	driver_id: z.number(),
	highest_finish: z.number(),
	is_eligible: z.string(),
	points: z.number(),
	position: z.number(),
	round_id: z.number(),
	round_number: z.number(),
	season_id: z.number(),
	session_id: z.number().nullable(),
	session_number: z.number(),
	win_count: z.number(),
	year: z.number(),
});

export type DriverChampionship = z.infer<typeof DriverChampionshipSchema>;

export class DriverChampionships extends BaseData<DriverChampionship> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_driverchampionship.csv';

	protected override schema = DriverChampionshipSchema;
}
