import { z } from 'zod';

import { BaseData } from './BaseData';

const SessionSchema = z.object({
	id: z.number(),
	date: z.string(),
	is_cancelled: z.string(),
	number: z.number().nullable(),
	point_system_id: z.number(),
	round_id: z.number(),
	scheduled_laps: z.number().nullable(),
	time: z.string().nullable(),
	type: z.string(),
});

export type Session = z.infer<typeof SessionSchema>;

export class Sessions extends BaseData<Session> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_session.csv';

	protected override schema = SessionSchema;

	constructor() {
		super();
		this.loadData();
	}

	public getData(): ReadonlyArray<Session> {
		return this.data;
	}

	public getSessionById(id: number): Session | undefined {
		return this.data.find((session) => session.id === id);
	}

	public getSessionsByRoundId(roundId: number): ReadonlyArray<Session> {
		return this.data.filter((session) => session.round_id === roundId);
	}

	public getSessionsByType(type: string): ReadonlyArray<Session> {
		return this.data.filter((session) => session.type === type);
	}

	public getCancelledSessions(): ReadonlyArray<Session> {
		return this.data.filter((session) => session.is_cancelled === 't');
	}

	public getActiveSessions(): ReadonlyArray<Session> {
		return this.data.filter((session) => session.is_cancelled === 'f');
	}

	public getSessionsByPointSystemId(pointSystemId: number): ReadonlyArray<Session> {
		return this.data.filter((session) => session.point_system_id === pointSystemId);
	}
}
