import { z } from 'zod';

import { BaseData } from './BaseData';

const SessionSchema = z.object({
	id: z.number(),
	date: z.string(),
	is_cancelled: z.string(),
	number: z.number().nullable(),
	point_system_id: z.number(),
	round_id: z.number(),
	scheduled_laps: z.number().nullable(),
	time: z.string().nullable(),
	type: z.string(),
});

export type Session = z.infer<typeof SessionSchema>;

export class Sessions extends BaseData<Session> {
	// eslint-disable-next-line @cspell/spellchecker
	protected override filename = 'formula_one_session.csv';

	protected override schema = SessionSchema;
}
