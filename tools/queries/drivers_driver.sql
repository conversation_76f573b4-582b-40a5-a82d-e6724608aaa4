SELECT
	`X`.`abbreviation` AS `code`,
	`X`.`date_of_birth` AS `dob`,
	`X`.`reference` AS `driverRef`,
	`X`.`forename` || ' ' || `X`.`surname` AS `name`,
	`X`.`nationality`,
	(SELECT COUNT(*) WHERE `X`.`position` = 1) AS `lapsLead`,
	(SELECT 0) AS `poles`,
	(SELECT 0) AS `podiums`,
	(SELECT 0) AS `raceWins`,
	(SELECT 0) AS `totalLaps`,
	(SELECT 0) AS `totalRaces`
FROM (
	SELECT
		*
	FROM `Drivers` AS `D`
		LEFT OUTER JOIN `laps` AS `L`
		LEFT OUTER JOIN `SessionEntries` AS `SE`
		LEFT OUTER JOIN `RoundEntries` AS `RE`
		LEFT OUTER JOIN `TeamDrivers` AS `TD`
	ON
		`L`.`session_entry_id` =  `SE`.`id`
			AND
		`SE`.`round_entry_id` = `RE`.`id`
			AND
		`RE`.`team_driver_id` = `TD`.`id`
			AND
		`TD`.`driver_id` = `D`.`id`
	WHERE `D`.`id` = :driverId
) AS `X`;
